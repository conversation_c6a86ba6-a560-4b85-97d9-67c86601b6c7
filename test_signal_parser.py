#!/usr/bin/env python3
"""
Test skryptu parsowania sygnałów bez potrzeby połączenia z Discord/Bybit.
Testuje tylko funkcjonalność regex i parsowania sygnałów.
"""

import re
import sys
import os

# Dodaj ścieżkę do głównego skryptu
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_signal_parsing():
    """Test parsowania różnych formatów sygnałów."""

    # Regex z głównego skryptu - zaktualizowany
    signal_pattern = re.compile(
        r"(?P<side>BUY|SELL)\s+(?P<pair>[A-Z0-9]+)\.?P?\s*"
        r".*?"
        r"Entry\s*[-:]\s*(?P<entry>[0-9]*\.?[0-9]+)\s*"
        r".*?"
        r"TP\s*[-:]\s*(?P<tp>[0-9]*\.?[0-9]+)\s*"
        r".*?"
        r"SL\s*[-:]\s*(?P<sl>[0-9]*\.?[0-9]+)\s*"
        r".*?"
        r"Time(?:frame)?[:\s]*(?P<tf>[0-9]+)",
        re.IGNORECASE | re.DOTALL
    )

    # Przykładowe sygnały do testowania
    test_signals = [
        """BUY BTCUSDT
Entry - 45000
TP - 46000
SL - 44000
Timeframe: 60""",

        """SELL ETH.P
Entry - 2500.50
TP - 2400.00
SL - 2600.00
Time: 30""",

        """BUY ADAUSDT
Some additional text here
Entry - 0.45
TP - 0.50
SL - 0.40
Timeframe: 120""",

        # Niepoprawny sygnał
        """This is not a signal
Just some random text"""
    ]

    print("🧪 Test parsowania sygnałów tradingowych\n")

    for i, signal_text in enumerate(test_signals, 1):
        print(f"Test {i}:")
        print(f"Tekst: {repr(signal_text[:50])}...")

        match = signal_pattern.search(signal_text)
        if match:
            data = match.groupdict()
            # Normalizacja nazwy pary - usuń .P i dodaj USDT jeśli nie ma
            pair = data['pair'].upper().replace('.P','')
            if not pair.endswith('USDT'):
                pair += 'USDT'
            side = data['side'].upper()

            try:
                entry = float(data['entry'])
                tp = float(data['tp'])
                sl = float(data['sl'])
                tf = int(data['tf'])

                print(f"✅ Rozpoznano sygnał:")
                print(f"   Para: {pair}")
                print(f"   Kierunek: {side}")
                print(f"   Entry: {entry}")
                print(f"   TP: {tp}")
                print(f"   SL: {sl}")
                print(f"   Timeframe: {tf} min")

                # Sprawdź logikę sygnału
                if side == 'BUY':
                    if tp <= entry:
                        print(f"⚠️  Ostrzeżenie: TP ({tp}) powinno być wyższe niż Entry ({entry}) dla BUY")
                    if sl >= entry:
                        print(f"⚠️  Ostrzeżenie: SL ({sl}) powinno być niższe niż Entry ({entry}) dla BUY")
                else:  # SELL
                    if tp >= entry:
                        print(f"⚠️  Ostrzeżenie: TP ({tp}) powinno być niższe niż Entry ({entry}) dla SELL")
                    if sl <= entry:
                        print(f"⚠️  Ostrzeżenie: SL ({sl}) powinno być wyższe niż Entry ({entry}) dla SELL")

            except ValueError as e:
                print(f"❌ Błąd konwersji wartości: {e}")
        else:
            print("❌ Nie rozpoznano sygnału")

        print("-" * 50)

def test_database_connection():
    """Test połączenia z bazą danych."""
    import sqlite3

    print("\n🗄️  Test połączenia z bazą danych")

    try:
        conn = sqlite3.connect('signals.db')
        cursor = conn.cursor()

        # Sprawdź strukturę tabeli
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='signals'")
        table_exists = cursor.fetchone()

        if table_exists:
            print("✅ Tabela 'signals' istnieje")

            # Sprawdź liczbę rekordów
            cursor.execute("SELECT COUNT(*) FROM signals")
            count = cursor.fetchone()[0]
            print(f"📊 Liczba sygnałów w bazie: {count}")

            # Pokaż ostatnie 3 sygnały
            if count > 0:
                cursor.execute("SELECT pair, side, entry, status FROM signals ORDER BY timestamp DESC LIMIT 3")
                recent = cursor.fetchall()
                print("🔍 Ostatnie sygnały:")
                for signal in recent:
                    print(f"   {signal[1]} {signal[0]} @ {signal[2]} (status: {signal[3]})")
        else:
            print("❌ Tabela 'signals' nie istnieje")

        conn.close()
        print("✅ Połączenie z bazą danych działa poprawnie")

    except Exception as e:
        print(f"❌ Błąd połączenia z bazą danych: {e}")

if __name__ == '__main__':
    test_signal_parsing()
    test_database_connection()
    print("\n🎉 Testy zakończone!")
